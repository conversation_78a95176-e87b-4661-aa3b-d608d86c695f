# Tita's Baby Shop - Admin Panel

Een moderne Flask web applicatie voor het beheren van baby kleding items met een donker thema.

## Features

### ✨ Hoofdfuncties
- **Modern donker thema** - Professionele uitstraling met Bootstrap 5 dark theme
- **Beveiligde admin toegang** - Wachtwoord bescherming voor alle admin functies
- **Admin formulier** (`/new`) voor het toevoegen van nieuwe items
- **Configureerbare keuze velden** via web interface (`/config`)
- **Foto upload** - Bestanden uploaden of direct foto maken met camera
- **Unieke ID generatie** voor elk item
- **Zoekfunctionaliteit** - Zoeken op ID, brand, category, geslacht of maat
- **Beheer tabel** - Overzicht van alle items met acties
- **Configuratie pagina** - Eenvoudig bewerken van alle keuze opties
- **Responsive design** - Werkt op desktop, tablet en mobiel

### 📋 Formulier Velden
- **Geslacht**: jongen/meisje (configureerbaar)
- **Maat**: Baby/kinder maten (configureerbaar)
- **Prijs**: Euro valuta veld
- **Brand**: Merken (configureerbaar)
- **Category**: sweater/T-shirt/broek/hemd/etc (configureerbaar)
- **Foto**: Upload bestand of maak foto met camera
- **ID**: Automatisch gegenereerde unieke identifier

## 🚀 Installatie & Gebruik

### Vereisten
- Python 3.8+
- Moderne webbrowser met camera ondersteuning (optioneel)

### Stappen
1. **Installeer dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start de applicatie:**
   ```bash
   python app.py
   ```

3. **Open in browser:**
   - Lokaal: http://127.0.0.1:5000
   - Netwerk: http://[je-ip-adres]:5000

4. **Login met admin gegevens:**
   - Gebruikersnaam: `admin`
   - Wachtwoord: `admin123`
   - **⚠️ Wijzig deze gegevens in productie!**

## 📁 Project Structuur

```
Website_Kleren/
├── app.py                 # Hoofd Flask applicatie
├── models.py              # Database modellen
├── forms.py               # WTForms formulieren
├── config.json            # Configuratie voor keuze velden
├── requirements.txt       # Python dependencies
├── templates/             # HTML templates
│   ├── base.html         # Basis template met navigatie
│   ├── new.html          # Nieuw item formulier
│   └── manage.html       # Items beheer tabel
├── static/               # Statische bestanden
│   ├── style.css         # Custom dark theme CSS
│   ├── app.js            # JavaScript functionaliteit
│   └── uploads/          # Geüploade afbeeldingen
└── titas_baby_shop.db    # SQLite database (wordt automatisch aangemaakt)
```

## ⚙️ Configuratie

### Keuze Velden Aanpassen via Web Interface
Ga naar `/config` in de applicatie om alle keuze opties eenvoudig aan te passen:

- **Intuïtieve interface** - Bewerk alle opties in één overzichtelijke pagina
- **Live preview** - Zie direct hoe je wijzigingen eruit zien
- **Validatie** - Automatische controle dat elk veld minimaal één waarde heeft
- **Komma-gescheiden** - Voer waarden in gescheiden door komma's
- **Direct actief** - Wijzigingen zijn meteen beschikbaar voor nieuwe items

### Handmatige Configuratie (Optioneel)
Je kunt ook direct `config.json` bewerken:

```json
{
  "geslacht": ["jongen", "meisje"],
  "maat": ["50", "56", "62", "68", "74", "80", "86", "92", "98", "104", "110", "116", "122", "128", "134", "140", "146", "152", "158", "164", "170", "176"],
  "brand": ["H&M", "Zara", "C&A", "Primark", "Next", "Carter's", "Petit Bateau", "Bonpoint", "Jacadi", "IKKS", "Catimini"],
  "category": ["sweater", "T-shirt", "broek", "hemd", "jurk", "rok", "short", "jas", "vest", "pyjama", "ondergoed", "sokken", "schoenen", "accessoires"]
}
```

### Database
- Gebruikt SQLite voor eenvoud
- Database wordt automatisch aangemaakt bij eerste start
- Bestand: `titas_baby_shop.db`

## 📱 Camera Functionaliteit

De applicatie ondersteunt:
- **Bestand upload** - Traditionele bestand selectie
- **Camera capture** - Direct foto maken in de browser
- **Automatische resize** - Afbeeldingen worden geoptimaliseerd
- **Preview** - Bekijk foto voor opslaan

## 🔐 Authenticatie & Beveiliging

### Admin Login
- **Beveiligde toegang** - Alle admin functies zijn wachtwoord beschermd
- **Session management** - Automatische uitlog bij inactiviteit
- **Standaard gegevens** (wijzig in productie):
  - Gebruikersnaam: `admin`
  - Wachtwoord: `admin123`

### Beveiligde Pagina's
- `/new` - Nieuw item toevoegen
- `/manage` - Items beheren en zoeken
- `/config` - Configuratie aanpassen
- `/delete/<id>` - Item verwijderen

### Login Functies
- **Wachtwoord zichtbaarheid toggle** - Toon/verberg wachtwoord
- **Automatische focus** - Cursor in gebruikersnaam veld
- **Foutafhandeling** - Duidelijke foutmeldingen
- **Redirect functionaliteit** - Terug naar gewenste pagina na login

## 🔍 Zoeken & Beheren

### Zoekfuncties
- Zoek op item ID (volledige of gedeeltelijke match)
- Zoek op brand naam
- Zoek op category
- Zoek op geslacht
- Zoek op maat

### Beheer Acties
- **Bekijk foto** - Klik op thumbnail voor grote weergave
- **Kopieer ID** - Klik op clipboard icoon
- **Verwijder item** - Met bevestiging dialog
- **Uitloggen** - Via dropdown menu in navigatie

## 🎨 Design Features

- **Dark Theme** - Moderne donkere interface
- **Bootstrap 5** - Responsive en professioneel
- **Bootstrap Icons** - Consistente iconografie
- **Custom CSS** - Aangepaste styling voor optimale UX
- **Smooth Animations** - Subtiele overgangen en effecten

## 🔧 Technische Details

### Tech Stack
- **Backend**: Flask 3.0.2
- **Database**: SQLAlchemy + SQLite
- **Forms**: Flask-WTF + WTForms
- **Frontend**: Bootstrap 5 + Custom CSS/JS
- **Images**: Pillow voor verwerking

### Security Features
- **Admin authenticatie** - Wachtwoord bescherming voor alle admin functies
- **Session management** - Veilige sessie handling
- **CSRF bescherming** - Via Flask-WTF voor alle formulieren
- **Bestand type validatie** - Alleen toegestane afbeelding formaten
- **Secure filename handling** - Veilige bestandsnaam verwerking
- **Input sanitization** - Automatische input validatie en cleaning

## 📝 Gebruik Tips

1. **Eerste gebruik**: Start met het toevoegen van een paar test items
2. **Foto's**: Gebruik de camera functie voor snelle foto's
3. **Zoeken**: Gebruik gedeeltelijke zoektermen voor betere resultaten
4. **ID's**: Kopieer volledige ID's voor externe referenties
5. **Configuratie**: Gebruik de `/config` pagina om keuze opties aan te passen
6. **Live preview**: Bekijk in de config pagina hoe je wijzigingen eruit zien
7. **Backup**: Maak regelmatig een backup van je `config.json` en database

## 🚀 Productie Deployment

Voor productie gebruik:
1. **Wijzig admin wachtwoord** - Update `ADMIN_PASSWORD_HASH` in `app.py`
2. **Wijzig SECRET_KEY** - Gebruik een sterke, unieke secret key
3. **Gebruik productie WSGI server** - Bijv. Gunicorn of uWSGI
4. **Configureer reverse proxy** - Bijv. Nginx voor SSL en load balancing
5. **Overweeg PostgreSQL** - Voor betere prestaties en schaalbaarheid
6. **Implementeer backup strategie** - Voor uploads en database
7. **Verwijder debug info** - Haal standaard login gegevens weg uit login pagina

---

**Gemaakt voor Tita's Baby Shop** 👶✨
