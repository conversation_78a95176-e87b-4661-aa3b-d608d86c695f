from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from werkzeug.utils import secure_filename
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
import os
import uuid
from models import db, Item
from forms import <PERSON>emForm, Search<PERSON>orm, ConfigForm, LoginForm
from PIL import Image
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-this-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///titas_baby_shop.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Admin configuration
ADMIN_USERNAME = 'nikita'
ADMIN_PASSWORD_HASH = generate_password_hash('Fa210808!')  # Change this password!

# Upload configuration
UPLOAD_FOLDER = 'static/uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Initialize database
db.init_app(app)

def login_required(f):
    """Decorator to require admin login for protected routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            flash('Je moet inloggen om deze pagina te bekijken.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif'}

def resize_image(image_path, max_size=(800, 800)):
    """Resize image to reduce file size while maintaining aspect ratio"""
    try:
        with Image.open(image_path) as img:
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            img.save(image_path, optimize=True, quality=85)
    except Exception as e:
        print(f"Error resizing image: {e}")

@app.route('/')
def index():
    if session.get('logged_in'):
        return redirect(url_for('manage'))
    else:
        return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    # If already logged in, redirect to manage page
    if session.get('logged_in'):
        return redirect(url_for('manage'))

    form = LoginForm()

    if form.validate_on_submit():
        username = form.username.data
        password = form.password.data

        if username == ADMIN_USERNAME and check_password_hash(ADMIN_PASSWORD_HASH, password):
            session['logged_in'] = True
            session['username'] = username
            flash('Succesvol ingelogd!', 'success')

            # Redirect to the page they were trying to access, or manage page
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('manage'))
        else:
            flash('Ongeldige gebruikersnaam of wachtwoord.', 'error')

    return render_template('login.html', form=form)

@app.route('/logout')
def logout():
    session.clear()
    flash('Je bent uitgelogd.', 'success')
    return redirect(url_for('login'))

@app.route('/new', methods=['GET', 'POST'])
@login_required
def new_item():
    form = ItemForm()
    
    if form.validate_on_submit():
        # Handle file upload
        foto_filename = None
        if form.foto.data:
            file = form.foto.data
            if file and allowed_file(file.filename):
                # Generate unique filename
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                
                file.save(file_path)
                resize_image(file_path)  # Resize to reduce file size
                foto_filename = unique_filename
        
        # Create new item
        item = Item(
            geslacht=form.geslacht.data,
            maat=form.maat.data,
            prijs=form.prijs.data,
            brand=form.brand.data,
            category=form.category.data,
            foto_filename=foto_filename
        )
        
        db.session.add(item)
        db.session.commit()
        
        flash(f'Item {item.id} succesvol toegevoegd!', 'success')
        return redirect(url_for('manage'))
    
    return render_template('new.html', form=form)

@app.route('/manage')
@login_required
def manage():
    search_form = SearchForm()
    search_query = request.args.get('search', '')
    
    if search_query:
        items = Item.query.filter(
            db.or_(
                Item.id.contains(search_query),
                Item.brand.contains(search_query),
                Item.category.contains(search_query),
                Item.geslacht.contains(search_query),
                Item.maat.contains(search_query)
            )
        ).order_by(Item.created_at.desc()).all()
    else:
        items = Item.query.order_by(Item.created_at.desc()).all()
    
    return render_template('manage.html', items=items, search_form=search_form, search_query=search_query)

@app.route('/delete/<item_id>')
@login_required
def delete_item(item_id):
    item = Item.query.get_or_404(item_id)

    # Delete associated image file if exists
    if item.foto_filename:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], item.foto_filename)
        if os.path.exists(file_path):
            os.remove(file_path)

    db.session.delete(item)
    db.session.commit()

    flash(f'Item {item_id} succesvol verwijderd!', 'success')
    return redirect(url_for('manage'))

def load_config():
    """Load configuration from config.json"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_config(config_data):
    """Save configuration to config.json"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)

@app.route('/config', methods=['GET', 'POST'])
@login_required
def config_page():
    form = ConfigForm()

    if form.validate_on_submit():
        try:
            # Get config data from form
            config_data = form.get_config_dict()

            # Validate that all fields have at least one option
            for field_name, values in config_data.items():
                if not values:
                    flash(f'Het veld "{field_name}" moet minimaal één waarde bevatten.', 'error')
                    return render_template('config.html', form=form)

            # Save the configuration
            save_config(config_data)

            flash('Configuratie succesvol opgeslagen! De nieuwe opties zijn nu beschikbaar in het formulier.', 'success')
            return redirect(url_for('config_page'))

        except Exception as e:
            flash(f'Fout bij het opslaan van de configuratie: {str(e)}', 'error')

    return render_template('config.html', form=form)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
